<template>
  <base-dialog
    width="80vw"
    append-to-body
    :show-dialog="visible"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :title="currentPlan?.id ? '编辑预报方案' : '新增预报方案'"
    :btn-loading="btnLoading"
    lock-scroll
    top="0"
    align-center
  >
    <div class="detail-dialog">
      <div class="basic-info">
        <div class="section-title">基本信息</div>
        <el-form :model="formData" label-width="100px" class="form">
          <el-form-item label="预报时段:">
            <div class="time-range">{{ formData.beginTime }} 至 {{ formData.endTime }}</div>
          </el-form-item>
          <el-form-item label="方案名称:">
            <el-input
              v-model="formData.forecastName"
              placeholder="请输入方案名称"
              :maxlength="50"
            />
          </el-form-item>
          <el-form-item label="备注:">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              :maxlength="200"
              style="--el-input-bg-color: transparent"
            />
          </el-form-item>
        </el-form>
      </div>

      <div class="forecast-setting">
        <div class="section-title">预报场景设置</div>
        <div class="setting-desc">
          未来降雨默认值为该预报时段的滚动预报值，未来水库出库流量默认值为模型滚动计算的规则调度出库流量值
        </div>
        <div class="rain-info">
          <!-- <span>已输入未来总降雨量：{{ totalRain?.toFixed(2) }} mm</span> -->
          <div>
            <el-date-picker
              v-model="fastEditModel.range"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :disabled-date="disabledRangeDate"
            />
          </div>
          <base-button @click="resetDataVoMap">恢复默认</base-button>
        </div>
        <el-table
          :data="detailData.forecastMainFutureDataVoMap"
          style="width: 100%"
          height="400px"
          class="forecast-table c-blue-el-table"
          border
        >
          <el-table-column prop="futureDateStr" label="未来时间" width="120">
            <!-- <template #default="{ row }">
                <el-input v-model="row.futureDateStr"></el-input>
              </template> -->
            <template #default="{ row }">
              {{ row.futureDateStr }}
            </template>
          </el-table-column>
          <el-table-column prop="futureRainfall" label="未来降雨量" width="120">
            <template #default="{ row }">
              <el-input
                v-model.number="row.futureRainfall"
                clearable
                style="--el-input-bg-color: transparent; --el-text-color-regular: #fff"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(value, key) in detailData.stnm"
            :key="key"
            :label="value"
            :prop="key"
          >
            <template #default="{ row }">
              <el-input
                v-model.number="row[key]"
                style="--el-input-bg-color: transparent; --el-text-color-regular: #fff"
                clearable
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </base-dialog>
</template>

<script lang="ts" setup>
import { useWatcher } from 'alova/client'
import { getForecastDetailForEdit, saveDiyForecastMain } from '@/alova_api/methods/forecast'
import { ForecastPlanDetail } from '@/alova_api/type'

// 接收预报ID，如果为undefined则为新增模式
const props = defineProps({
  currentPlan: {
    type: Object as PropType<Partial<ForecastPlanDetail>>,
    default: () => {}
  },
  addBaseForecastId: {
    type: [Number, String],
    default: undefined
  }
})

const visible = defineModel<boolean>({
  default: false
})

const emit = defineEmits(['success'])

// 表单数据，初始为默认值
const formData = ref<Partial<ForecastPlanDetail>>({
  beginTime: '',
  endTime: '',
  forecastName: '',
  remark: '',
  ...props.currentPlan
})

const fastEditModel = reactive<{
  range: [Date, Date] | [] //时间范围
  totalRain?: number //总雨量
  rrType: string // 水库类型
  totalOutQ?: number //总出库流量
}>({
  range: [],
  totalRain: undefined,
  rrType: '',
  totalOutQ: undefined
})

const disabledRangeDate = (date: Date) => {
  let allDate = false
  // console.log('disabledRangeDate', date)

  return allDate
}

const lastForecastMainFutureDataVoMap: ForecastPlanDetail['forecastMainFutureDataVoMap'] = []
const { data: detailData, send: fetchDetail } = useWatcher(
  () => getForecastDetailForEdit(Number(props.currentPlan?.id || props.addBaseForecastId)),
  [() => props.currentPlan?.id, () => props.addBaseForecastId],
  {
    immediate: false,
    initialData: {},
    force: true
  }
).onSuccess(() => {
  formData.value = {
    ...props.currentPlan,
    ...detailData.value
  }
  lastForecastMainFutureDataVoMap.splice(
    0,
    lastForecastMainFutureDataVoMap.length,
    ...JSON.parse(JSON.stringify(detailData.value.forecastMainFutureDataVoMap || []))
  )
})

const totalRain = computed(() => {
  return (
    detailData.value.forecastMainFutureDataVoMap?.reduce((acc, cur) => {
      return acc + +(cur.futureRainfall || 0)
    }, 0) || 0
  )
})



const allAllowDate = computed(() => {
  const allowDate = getTimeRangeForStr(detailData.value.beginTime, detailData.value.endTime)
})

watch(
  () => visible.value,
  val => {
    if (val) {
      fetchDetail()
    }
  }
)

// 当详情数据加载完成后，更新表单数据
watch(
  () => detailData.value.forecastMainFutureDataVoMap,
  val => {
    if (val) {
      formData.value.forecastMainFutureDataVoMap = val
    }
  },
  { immediate: true }
)

const resetDataVoMap = () => {
  detailData.value.forecastMainFutureDataVoMap = [...lastForecastMainFutureDataVoMap]
}

const btnLoading = ref(false)

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!formData.value.forecastName) {
    ElMessage.error('请输入方案名称')
    return
  }

  try {
    btnLoading.value = true

    // 准备保存的数据，合并表单数据和详情数据
    const saveData: Partial<ForecastPlanDetail> = {
      ...detailData.value,
      ...formData.value,
      // 确保表格数据是最新的
      forecastMainFutureDataVoMap: detailData.value.forecastMainFutureDataVoMap
    }

    console.log('保存数据:', saveData)

    // 调用保存接口
    await saveDiyForecastMain(saveData)

    ElMessage.success('保存成功')
    visible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    btnLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.detail-dialog {
  max-height: 70vh;
  overflow-y: auto;
  color: #fff;

  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
    color: #7ff;
  }

  .basic-info {
    margin-bottom: 24px;

    .time-range {
      line-height: 32px;
    }
  }

  .forecast-setting {
    .setting-desc {
      margin-bottom: 16px;
      line-height: 1.5;
    }

    .rain-info {
      margin-bottom: 16px;
      font-weight: bold;
      color: #7ff;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .table-container {
      margin-bottom: 16px;
      overflow-y: auto;
    }
  }

  :deep(.el-form) {
    .el-form-item__label {
      color: #fff;
    }

    .el-input__wrapper,
    .el-textarea__wrapper {
      background-color: rgba(0, 38, 89, 0.6);
    }

    .el-input__inner,
    .el-textarea__inner {
      color: #fff;
    }
  }

  :deep(.forecast-table) {
    --el-table-bg-color: transparent;
    --el-table-header-bg-color: rgba(3, 86, 181, 0.2);
    --el-table-tr-bg-color: rgba(0, 38, 89, 0.6);
    --el-table-header-text-color: rgb(4, 100, 234);
    --el-table-text-color: #fff;
    --el-table-row-hover-bg-color: #06338f85;
    --el-table-border-color: rgba(127, 255, 255, 0.2);

    .el-table__row td {
      border-color: rgba(127, 255, 255, 0.2);
    }
  }
}
</style>

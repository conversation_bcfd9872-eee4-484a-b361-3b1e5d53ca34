<template>
  <div class="page-index danger-upload">
    <van-nav-bar title="隐患上报" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main">
      <!-- 排查时间 -->
      <div class="basic-date">{{ formData.pcsj }}</div>

      <van-form ref="formRef" class="form-card-box" :disabled="isDone" :show-error="false">
        <van-field
          readonly
          clickable
          name="picker"
          :value="formData.yhlxName"
          label="隐患类型"
          placeholder="请选择"
          right-icon="arrow"
          input-align="right"
          required
          :rules="[{ required: true, message: '请选择隐患类型' }]"
          error-message-align="right"
          :show-error-message="false"
          @click-input="showPicker = true"
        />
        <van-field
          name="status"
          label="状态"
          input-align="right"
          required
          :rules="[{ required: true, message: '请选择' }]"
          error-message-align="right"
          :show-error-message="false"
        >
          <template #input>
            <van-radio-group v-model="formData.status" direction="horizontal" :disabled="isDone">
              <van-radio :name="0">未整改</van-radio>
              <van-radio :name="1">已整改</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-model="formData.szwz"
          label="所在位置"
          placeholder="请输入"
          input-align="right"
          required
          :rules="[{ required: true, message: '请输入所在位置' }]"
          error-message-align="right"
          :show-error-message="false"
        />

        <van-field
          readonly
          clickable
          input-align="right"
          name="fssj"
          :value="formData.fssj"
          label="发生时间"
          placeholder="点击选择时间"
          required
          :rules="[{ required: true, message: '请选择' }]"
          error-message-align="right"
          :show-error-message="false"
          @click-input="showTimePopup('fssj')"
        />
        <van-field
          v-show="formData.status == 1"
          readonly
          clickable
          input-align="right"
          name="zgsj"
          :value="formData.zgsj"
          label="整改时间"
          placeholder="点击选择时间"
          @click-input="showTimePopup('zgsj')"
        />

        <van-field
          v-model="formData.yhqk"
          rows="4"
          autosize
          label="隐患情况"
          type="textarea"
          placeholder="请输入"
          show-word-limit
          maxlength="200"
          required
          :rules="[{ required: true, message: '请输入隐患情况' }]"
          :show-error-message="false"
        />
        <van-field name="zgqFileList" label="整改前">
          <template #input>
            <CustomUpload
              v-model="formData.zgqFileList"
              list-type="picture"
              :module-id="9"
              module="xjxc"
              :capture="capture"
              :disabled="isDone"
              :deletable="!isDone"
            />
          </template>
        </van-field>
        <van-field v-show="formData.status == 1" name="zghFileList" label="整改后">
          <template #input>
            <CustomUpload
              v-model="formData.zghFileList"
              list-type="picture"
              :module-id="9"
              module="xjxc"
              :capture="capture"
              :disabled="isDone"
              :deletable="!isDone"
            />
          </template>
        </van-field>
      </van-form>
    </div>
    <BottomBtn v-if="!isDone" confirmText="保存" cancelText="返回" @cancel="onClickLeft" @confirm="onSubmit" />

    <van-popup v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="label"
        :columns="getOptions('yhlx')"
        @confirm="onPickerConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
    <van-popup v-model="showTimePicker" position="bottom">
      <van-datetime-picker
        v-model="currentTime"
        type="datetime"
        v-bind="timeProps"
        @confirm="onTimeConfirm"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import BottomBtn from '@/components/BottomBtn.vue';
import CustomUpload from '@/components/CustomUpload/index.vue';
import { saveDangerRecord } from '@/api/dangerRecord';
import { getCurrentPosition } from '@/utils/location';

export default {
  name: 'DangerUpload',
  components: {
    BottomBtn,
    CustomUpload
  },
  props: {},
  data() {
    const isAndroid = /android/i.test(navigator.userAgent);
    return {
      capture: isAndroid ? 'camera' : null,
      isAdd: false,
      isDone: false,
      formData: {
        yhlx: '',
        yhlxName: '',
        status: 0,
        szwz: '',
        yhqk: '',
        fssj: '',
        pcsj: '',
        zgsj: '',
        zgqFileList: [],
        zghFileList: []
      },
      showPicker: false,
      showTimePicker: false,
      timeType: '',
      timeProps: {},
      currentTime: new Date()
    };
  },
  computed: {
    ...mapGetters('common', ['getOptions'])
  },
  watch: {},
  methods: {
    onClickLeft() {
      if (this.isDone) {
        this.back();
        return;
      }
      this.$dialog
        .confirm({
          title: '提示',
          message: '上报信息尚未保存，是否确认返回？'
        })
        .then(() => {
          this.back();
        })
        .catch(() => {});
    },
    back() {
      this.$router.go(-1);
    },
    onPickerConfirm(val) {
      this.formData.yhlxName = val.label;
      this.formData.yhlx = val.value;
      this.showPicker = false;
    },
    showTimePopup(type) {
      this.showTimePicker = true;
      this.timeType = type;
      this.currentTime = this.formData[type] ? new Date(+this.$dayjs(this.formData[type])) : new Date();
      if (type === 'fssj' && this.formData.zgsj) {
        this.$set(this.timeProps, 'maxDate', new Date(+this.$dayjs(this.formData.zgsj)));
        if (this.timeProps.minDate) {
          delete this.timeProps.minDate;
        }
      } else if (type === 'zgsj' && this.formData.fssj) {
        this.$set(this.timeProps, 'minDate', new Date(+this.$dayjs(this.formData.fssj)));
        if (this.timeProps.maxDate) {
          delete this.timeProps.maxDate;
        }
      }
    },
    onTimeConfirm(val) {
      this.formData[this.timeType] = this.$dayjs(val).format('YYYY-MM-DD HH:mm');
      this.showTimePicker = false;
    },
    onSubmit() {
      this.$refs.formRef.validate().then(valid => {
        console.log(valid, 'valid');
        const { zgqFileList, zghFileList, yhlxName, ...rest } = JSON.parse(JSON.stringify(this.formData));

        saveDangerRecord({
          ...rest,
          zgqFileId: zgqFileList ? zgqFileList.map(i => i.id).join(',') : null,
          zghFileId: zghFileList ? zghFileList.map(i => i.id).join(',') : null,
          fssj: this.formData.fssj ? this.$dayjs(this.formData.fssj).format('YYYY-MM-DD HH:mm:ss') : null,
          zgsj: this.formData.zgsj ? this.$dayjs(this.formData.zgsj).format('YYYY-MM-DD HH:mm:ss') : null
        }).then(res => {
          if (res.status === 200) {
            this.$userApp.toast.show({
              text: `上报成功`,
              type: 'text'
            });
            this.$router.go(-1);
          }
        });
      });
    }
  },
  created() {
    // 设置所在位置
    getCurrentPosition(p => {
      this.formData.szwz = p.addresses;
    });

    const params = this.$route.params;
    const { type, ...rest } = params;

    this.isAdd = type === 'add';
    this.isDone = params.status === 1;
    if (!this.isAdd) {
      const formData = JSON.parse(JSON.stringify({ ...rest }));
      this.formData = {
        ...formData,
        zgqFileList: formData.zgqFileList || [],
        zghFileList: formData.zghFileList || [],
        yhlxName: this.getOptions('yhlx').find(item => item.value === formData.yhlx)?.label
      };
    } else {
      this.formData = {
        yhlx: '',
        yhlxName: '',
        status: 0,
        szwz: '',
        yhqk: '',
        fssj: '',
        pcsj: this.$dayjs().format('YYYY-MM-DD HH:mm:ss'),
        zgsj: '',
        zgqFileList: [],
        zghFileList: [],
        taskId: params?.taskId || ''
      };
    }
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.danger-upload {
  padding-bottom: 134px;
  color: $color-text-main;
  background-color: $bg-page1;
  .basic-date {
    padding: 30px 20px;
    font-size: 28px;
    font-weight: 500;
  }
  .main {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 15px 15px 0;
    overflow-y: auto;
    .form-card-box {
      margin-bottom: 14px;
      background: $bg-page;
      border-radius: 30px;
      .input-box {
        &:last-child {
          border-bottom: none;
        }
      }
      &::v-deep {
        .van-cell {
          background: none;
        }
      }
    }
  }
}
.index-readonly {
  padding-bottom: 0;
}
</style>

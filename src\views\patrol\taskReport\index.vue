<template>
  <!-- 情况上报 -->
  <main class="main flex-column">
    <CustomNavBar />
    <section class="section flex-1 flex-column">
      <div class="section-head flex-vc">
        <img class="head-avatar" src="@/assets/images/patrol/patrolTask/avatar.png" alt="头像" />
        <div>
          <div class="head-name">检查人员：{{ userName }}</div>
          <div class="head-date">
            巡检日期： <span>{{ currentDate }}</span>
          </div>
        </div>
      </div>
      <van-form ref="formRef" class="section-content flex-1" :show-error="false">
        <van-field name="radio" label="巡检类型" required>
          <template #input>
            <van-radio-group @change="onXjlxChange" v-model="formData.xjlx" direction="horizontal">
              <van-radio v-for="item in typeOptions" :key="item.value" :name="item.value">{{ item.label }}</van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <van-field v-model="formData.jhmc" label="计划名称" required :rules="[{ required: true, message: '请输入' }]" />

        <van-field
          v-model="formData.xjry"
          rows="4"
          autosize
          required
          label="参与巡检人员"
          type="textarea"
          placeholder="请输入"
          show-word-limit
          maxlength="50"
          :rules="[{ required: true, message: '请输入' }]"
        />

        <van-field
          v-model="formData.xjnr"
          rows="4"
          autosize
          required
          label="巡检内容"
          type="textarea"
          placeholder="请输入"
          show-word-limit
          maxlength="50"
          :rules="[{ required: true, message: '请输入' }]"
        />

        <van-field name="radio" label="状态" required>
          <template #input>
            <van-radio-group v-model="formData.status" direction="horizontal">
              <van-radio v-for="item in situationOptions" :key="item.value" :name="item.value">{{ item.label }}</van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <van-field
          v-if="formData.status === 2"
          readonly
          clickable
          input-align="right"
          name="zgsj"
          key="zgsj"
          :value="formData.zgsj"
          label="整改时间"
          placeholder="点击选择时间"
          required
          :rules="[{ required: true, message: '请选择' }]"
          error-message-align="right"
          :show-error-message="false"
          @click-input="showTimePicker = true"
        />

        <van-field label="巡查路线" name="routeLine">
          <template #input>
            <div class="route-line">
              <span v-for="(item, index) in routeLine" :key="index">
                {{ item.pointName }} <em v-if="index != routeLine.length - 1">==》</em>
              </span>
            </div>
          </template>
        </van-field>

        <van-field name="xjtpFile" label="巡检图片">
          <template #input>
            <CustomUpload v-model="formData.xjtpFile" list-type="picture" :module-id="9" module="xjxc" :capture="capture" />
          </template>
        </van-field>

        <van-field v-if="formData.status === 2 || formData.status === 0" name="yhtpFile" label="隐患图片">
          <template #input>
            <CustomUpload v-model="formData.yhtpFile" list-type="picture" :module-id="9" module="xjxc" :capture="capture" />
          </template>
        </van-field>

        <van-field v-if="formData.status === 2" name="zghFileList" label="整改后图片">
          <template #input>
            <CustomUpload v-model="formData.zghFileList" list-type="picture" :module-id="9" module="xjxc" :capture="capture" />
          </template>
        </van-field>
      </van-form>
    </section>
    <BottomBtn cancel-text="返回" @cancel="onCancel" @confirm="onConfirm" />

    <van-popup v-model="showTimePicker" position="bottom">
      <van-datetime-picker
        v-model="currentTime"
        type="datetime"
        v-bind="timeProps"
        @confirm="onTimeConfirm"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </main>
</template>

<script>
import { mapState } from 'vuex';
import { Dialog } from 'vant';

import BottomBtn from '@/components/BottomBtn.vue';
import CustomUpload from '@/components/CustomUpload/index.vue';
import { situationOptions, typeOptions, routeLine } from '../options';
import { getStorage } from '@/utils/storage';
import { getGcxcDetail, addTask } from '@/api/patrol';

export default {
  name: 'PatrolReport',
  components: {
    BottomBtn,
    CustomUpload
  },
  data() {
    this.routeLine = routeLine;
    const currentDate = this.$dayjs().format('YYYY-MM-DD');
    const isAndroid = /android/i.test(navigator.userAgent);
    return {
      typeOptions,
      capture: isAndroid ? 'camera' : null,
      situationOptions,
      taskId: null,
      optType: '',
      currentDate,
      userName: '',
      formData: {
        xjlx: 1, // 巡检类型
        jhmc: '', // 计划名称
        xjry: '', // 参与巡检人员
        xjnr: '', // 巡检内容
        status: 1, // 状态
        xjtpFile: [], // 巡检图片
        yhtpFile: [], // 隐患图片
        zghFileList: [], // 整改后图片
        zgsj: '' // 整改时间
      },
      showTimePicker: false,
      currentTime: new Date(),
      timeProps: {}
    };
  },
  computed: {
    ...mapState('patrol', {
      routeArray: state => state.routeArray,
      distance: state => state.distance,
      xjhs: state => state.time
    }),
    ...mapState({
      projectInfo: state => state.user.projectInfo,
      userInfo: state => state.user.userInfo
    })
  },
  created() {
    this.onXjlxChange(1);

    const param = this.$route.query;
    this.taskId = +param.taskId;
    this.optType = param.type || '';
    const userInfo = getStorage('userInfo');
    if (userInfo) {
      this.userName = userInfo.displayName;
    }
    this.getDetail();
  },
  methods: {
    onXjlxChange(val) {
      if (!typeOptions[val - 1]) {
        return;
      }
      this.formData.jhmc = this.$dayjs().format('YYYY年MM月DD日') + typeOptions[val - 1].label + '巡检';
    },
    async getDetail() {
      const res = await getGcxcDetail({ id: this.taskId });
      const { xjrq = '', xjtpFile } = res.data;
      this.formData = JSON.parse(JSON.stringify(res.data));
      this.formData.xjrq = xjrq;
      this.formData.xjtpFile = xjtpFile;
    },
    onTimeConfirm(val) {
      this.formData.zgsj = this.$dayjs(val).format('YYYY-MM-DD HH:mm');
      this.showTimePicker = false;
    },
    // 提交
    onConfirm() {
      this.$refs.formRef.validate().then(valid => {
        // 实际的运动轨迹，插入到巡检路线的后面
        const guiji = this.routeArray.map(item => {
          return {
            ...item,
            lgtd: item.lng,
            lttd: item.lat,
            tm: item.time
          };
        });

        const params = {
          id: this.taskId,
          ...this.formData,
          rcglInspectionRouteList: [...routeLine, ...guiji], // 巡检路线
          xjhs: this.optType === 'detail' ? this.formData.xjhs : this.xjhs, // 巡检耗时
          xjtpId: this.formData.xjtpFile.map(i => i.id).join(), // 巡检图片id集合
          yhtpId: this.formData.status !== 1 && this.formData.yhtpFile ? this.formData.yhtpFile.map(i => i.id).join(',') : '',
          zghfjId:
            this.formData.status === 2 && this.formData.zghFileList ? this.formData.zghFileList.map(i => i.id).join(',') : '',
          wrpcd: this.userInfo.assignWrpcdList?.[0] || this.projectInfo.wrpcd, // 工程id，必传
          distance: this.optType === 'detail' ? this.formData.distance : this.distance, // 路程
          fzr: this.userInfo.displayName,
          zgsj: this.formData.zgsj ? this.$dayjs(this.formData.zgsj).format('YYYY-MM-DD HH:mm:ss') : null // 整改时间
        };

        addTask(params).then(res => {
          if (res.status === 200) {
            this.$store.commit('patrol/resetPatrol');
            this.$userApp.toast.show({
              text: this.optType === 'detail' ? '修改成功' : '已结束巡查',
              type: 'text'
            });
            setTimeout(() => {
              this.optType === 'detail' ? this.$router.back() : this.$router.push('/');
            }, 500);
          }
        });
      });
    },
    onCancel() {
      Dialog.confirm({
        title: '提示',
        message: '巡查信息未保存，返回后巡查数据不会录入，是否确认返回',
        cancelButtonText: '返回'
      })
        .then(() => {
          this.$router.back();
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding-top: 100px;
}
.section {
  padding-bottom: 145px;
  .section-head {
    box-sizing: border-box;
    height: 200px;
    padding-left: 48px;
    font-size: 28px;
    line-height: 36px;
    .head-avatar {
      width: 90px;
      height: 90px;
      margin-right: 20px;
    }
    .head-name {
      margin-bottom: 16px;
    }
    .head-date {
      font-size: 24px;
    }
  }
  .section-content {
    overflow: auto;
  }
}
.bottom-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  padding: 24px 46px;
  background: $bg-page;
  border-top: 1px solid $border-color1;
}
</style>

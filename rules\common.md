---
type: "always_apply"
---

# 编码习惯

## scss

scss编写时不要使用&-来简写类名，但可以使用&替代当前类名，如在类名里面嵌套&:选择器来指定该类下面的选择器等

## vue

1. defineProps一般不使用泛型，而是像下面这样直接配置类型和值
```vue
const props = defineProps({
  year: {
    type: String,
    default: () => new Date().getFullYear()
  }
})
```

# 项目结构

本工作空间有三个项目，管理端（std_web目录）、展示端（std-web-matrix目录）、h5（std_app目录）

## 管理端

### 技术栈

管理端采用vue2, + element ui等技术栈，详看std_web文件夹下面的 @package.json

## 展示端

### 技术栈

展示端使用vue3相关技术栈，详见 @package.json, 展示端配置了自动导入 @vite.config.ts ,对于vue的API、element plus的组件、src/components文件夹下面的组件均无需导入

### 请求说明

请求使用的是alova进行请求，alova的请求一般在src/alova_api/methods文件夹中定义，API用到的类型一般写在 @type.ts

### 布局说明

在 @index.vue 中定义了整体布局，基本上系统的页面都会在次布局中显示， @staticRouter.ts 中配置了对应的组件，启用了地图显示的组件，一般都是使用 @LeftRegion.vue 和 @RightRegion.vue 在主布局中显示只有两边区域的内容，如 @index.vue






